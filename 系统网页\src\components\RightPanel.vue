<script setup>
import { ref } from 'vue';
import VideoMonitor from './VideoMonitor.vue';
import SignManagement from './SignManagement.vue';
import GuideManagement from './GuideManagement.vue';

// 视频监控相关数据
const videoMonitorRef = ref(null);

// 指示牌管理相关数据
const signManagementRef = ref(null);

// 引导员管理相关数据
const guideManagementRef = ref(null);
const guideList = ref([
  { 
    id: 'G001', 
    name: '李师傅', 
    position: '地铁保安', 
    location: 'B1层A区', 
    phone: '18569874125', 
    status: 'online' 
  },
  { 
    id: 'G002', 
    name: '王师傅', 
    position: '安全员', 
    location: 'B2层B区', 
    phone: '13812345678', 
    status: 'busy' 
  },
  { 
    id: 'G003', 
    name: '张师傅', 
    position: '疏散员', 
    location: 'M层C区', 
    phone: '15987654321', 
    status: 'offline' 
  }
]);

// 处理视频监控事件
const handleVideoConnected = (cameraId) => {
  emit('video-connected', cameraId);
  console.log(`视频连接成功: ${cameraId}`);
};

const handleVideoDisconnected = () => {
  emit('video-disconnected');
  console.log('视频连接断开');
};

const handleVideoError = (error) => {
  emit('video-error', error);
  console.error('视频错误:', error);
};

const handleCameraSwitched = (cameraId) => {
  emit('camera-switched', cameraId);
  console.log(`摄像头切换到: ${cameraId}`);
};

const handleQualityChanged = (quality) => {
  emit('quality-changed', quality);
  console.log(`视频质量调整为: ${quality}`);
};

const handleScreenshotTaken = (data) => {
  emit('screenshot-taken', data);
  console.log('截图已保存:', data);
};

// 处理指示牌管理事件
const handleDeviceStatusChecked = (statusData) => {
  emit('device-status-checked', statusData);
  console.log('设备状态查询:', statusData);
};

const handleSignMessageSent = (messageData) => {
  emit('message-sent', messageData);
  console.log('指示牌消息发送:', messageData);
};

const handleGuideMessageSent = (messageData) => {
  emit('guide-message-sent', messageData);
  console.log('引导员消息发送:', messageData);
};

// 处理引导员管理事件
const handleGuideSelected = (guide) => {
  emit('guide-selected', guide);
  console.log('选中引导员:', guide);
};

const handleTaskAssigned = (task) => {
  emit('task-assigned', task);
  console.log('任务派发:', task);
};

const handleVoiceCallStarted = (callData) => {
  emit('voice-call-started', callData);
  console.log('语音通话开始:', callData);
};

const handleLocationUpdateRequested = (guideId) => {
  emit('location-update-requested', guideId);
  console.log(`请求位置更新: ${guideId}`);
};

const handleEmergencyCall = (emergencyData) => {
  emit('emergency-call', emergencyData);
  console.log('紧急呼叫:', emergencyData);
};

// 暴露事件给父组件
const emit = defineEmits([
  // 视频监控事件
  'video-connected',
  'video-disconnected',
  'video-error',
  'camera-switched',
  'quality-changed',
  'screenshot-taken',
  // 指示牌管理事件
  'device-status-checked',
  'message-sent',
  // 引导员管理事件
  'guide-selected',
  'task-assigned',
  'voice-call-started',
  'guide-message-sent',
  'location-update-requested',
  'emergency-call'
]);

// 可以通过props接收配置
const props = defineProps({
  // 视频监控设备配置
  deviceIP: {
    type: String,
    default: '***********'
  },
  devicePort: {
    type: Number,
    default: 80
  },
  username: {
    type: String,
    default: 'admin'
  },
  password: {
    type: String,
    default: 'hucom12345'
  },
  autoConnectVideo: {
    type: Boolean,
    default: false
  },
  showVideoControls: {
    type: Boolean,
    default: true
  },
  defaultCamera: {
    type: Number,
    default: 1
  },
  signs: {
    type: Array,
    default: () => []
  },
  guides: {
    type: Array,
    default: () => []
  },
  defaultSignId: {
    type: String,
    default: 'D001'
  },
  defaultGuideId: {
    type: String,
    default: 'G001'
  },
  readonly: {
    type: Boolean,
    default: false
  }
});

// 如果有外部数据，使用外部数据

if (props.guides && props.guides.length > 0) {
  guideList.value = props.guides;
}

// 暴露方法给父组件
defineExpose({
  // 视频监控方法
  connectVideo: () => videoMonitorRef.value?.connectVideo(),
  disconnectVideo: () => videoMonitorRef.value?.disconnectVideo(),
  switchCamera: (cameraId) => videoMonitorRef.value?.switchCamera(cameraId),
  takeScreenshot: () => videoMonitorRef.value?.takeScreenshot(),
  
  // 指示牌管理方法
  checkDeviceStatus: () => signManagementRef.value?.checkDeviceStatus(),
  sendMessage: () => signManagementRef.value?.sendMessage(),
  
  // 引导员管理方法
  selectGuide: (guideId) => guideManagementRef.value?.selectGuide(guideId),
  assignTask: (guideId, task) => guideManagementRef.value?.assignTask(guideId, task),
  startVoiceCall: (guideId) => guideManagementRef.value?.startVoiceCall(guideId),
  
  // 获取当前状态
  getVideoStatus: () => videoMonitorRef.value?.getStatus(),
  getDeviceStatus: () => signManagementRef.value?.getDeviceStatus(),
  getOperationLogs: () => signManagementRef.value?.getOperationLogs(),
  getCurrentGuide: () => guideManagementRef.value?.getCurrentGuide()
});
</script>

<template>
  <div class="right-panel">
    <!-- 7. 视频监控 -->
    <VideoMonitor
      ref="videoMonitorRef"
      :device-i-p="props.deviceIP"
      :device-port="props.devicePort"
      :username="props.username"
      :password="props.password"
      :auto-connect="props.autoConnectVideo"
      :show-controls="props.showVideoControls"
      :default-camera="props.defaultCamera"
      @video-connected="handleVideoConnected"
      @video-disconnected="handleVideoDisconnected"
      @video-error="handleVideoError"
      @camera-switched="handleCameraSwitched"
      @quality-changed="handleQualityChanged"
      @screenshot-taken="handleScreenshotTaken"
    />

    <!-- 8. 指示牌管理 -->
    <SignManagement
      ref="signManagementRef"
      :default-device-id="props.defaultSignId || 'D001'"
      :readonly="props.readonly"
      @device-status-checked="handleDeviceStatusChecked"
      @message-sent="handleSignMessageSent"
    />

    <!-- 9. 引导员管理 -->
    <GuideManagement 
      ref="guideManagementRef"
      :guides="guideList"
      :default-guide-id="props.defaultGuideId"
      :readonly="props.readonly"
      @guide-selected="handleGuideSelected"
      @task-assigned="handleTaskAssigned"
      @voice-call-started="handleVoiceCallStarted"
      @guide-message-sent="handleGuideMessageSent"
      @location-update-requested="handleLocationUpdateRequested"
      @emergency-call="handleEmergencyCall"
    />
  </div>
</template>

<style scoped>
/* 右侧面板样式 */
.right-panel {
  width: 22%;
  padding: 5px 5px 0px 0px;
  display: flex;
  flex-direction: column;
  gap: 5px;
  overflow-y: visible;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .right-panel {
    width: 25%;
  }
}

@media (max-width: 768px) {
  .right-panel {
    width: 100%;
    padding: 5px;
    max-height: 400px;
  }
}
</style>
