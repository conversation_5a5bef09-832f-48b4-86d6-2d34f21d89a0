<script setup>
import { ref } from 'vue';
import BuildingStatus from './BuildingStatus.vue';
import StatusReport from './StatusReport.vue';
import SimulationControl from './SimulationControl.vue';

// 建筑状态数据
const buildingStatusData = ref({
  status: '密度过高',
  statusType: 'red',
  description: '当前状态：正在疏散...'
});

// 状态上报数据已移至App.vue统一管理

// 处理仿真控制事件
const handleSimulationStart = () => {
  console.log('疏散仿真开始');
  emit('simulation-started');
};

const handleSimulationComplete = () => {
  console.log('疏散仿真完成');
  emit('simulation-completed');
};

const handleOptimizationStart = () => {
  console.log('路径优化开始');
  emit('optimization-started');
};

const handleOptimizationComplete = () => {
  console.log('路径优化完成');
  emit('optimization-completed');
};

const handleSignUpdateStart = () => {
  console.log('指示牌更新开始');
  emit('sign-update-started');
};

const handleSignUpdateComplete = () => {
  console.log('指示牌更新完成');
  emit('sign-update-completed');
};

// 暴露事件给父组件
const emit = defineEmits([
  'report-updated',
  'simulation-started',
  'simulation-completed',
  'optimization-started',
  'optimization-completed',
  'sign-update-started',
  'sign-update-completed'
]);

// 可以通过props接收配置
const props = defineProps({
  buildingStatus: {
    type: Object,
    default: () => ({})
  },
  reports: {
    type: Array,
    default: () => []
  },
  simulationDisabled: {
    type: Boolean,
    default: false
  },
  alertStatus: {
    type: Object,
    default: () => ({})
  }
});

// 如果有外部数据，使用外部数据
if (props.buildingStatus && Object.keys(props.buildingStatus).length > 0) {
  buildingStatusData.value = { ...buildingStatusData.value, ...props.buildingStatus };
}

// 暴露方法给父组件
defineExpose({
  updateBuildingStatus: (newStatus) => {
    buildingStatusData.value = { ...buildingStatusData.value, ...newStatus };
  }
});
</script>

<template>
  <div class="left-panel">
    <!-- 1. 建筑状态显示 -->
    <BuildingStatus
      :building-status="props.buildingStatus"
      :alert-status="props.alertStatus"
    />

    <!-- 2. 实时状态上报 -->
    <StatusReport
      :reports="props.reports"
      :alert-status="props.alertStatus"
    />

    <!-- 3. 疏散仿真与优化 -->
    <SimulationControl 
      :disabled="props.simulationDisabled"
      @simulation-start="handleSimulationStart"
      @simulation-complete="handleSimulationComplete"
      @optimization-start="handleOptimizationStart"
      @optimization-complete="handleOptimizationComplete"
      @sign-update-start="handleSignUpdateStart"
      @sign-update-complete="handleSignUpdateComplete"
    />
  </div>
</template>

<style scoped>
/* 左侧面板样式 */
.left-panel {
  width: 22%;
  padding: 5px 0px 0px 5px;
  overflow-y: visible;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .left-panel {
    width: 25%;
  }
}

@media (max-width: 768px) {
  .left-panel {
    width: 100%;
    padding: 5px;
    max-height: 300px;
  }
}
</style>
