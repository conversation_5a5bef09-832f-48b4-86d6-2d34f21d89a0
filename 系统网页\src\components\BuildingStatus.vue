<script setup>
import { ref } from 'vue';

// 建筑状态数据
const buildingStatus = ref({
  status: '密度过高',
  statusType: 'red', // red, yellow, green
  description: '当前状态：正在疏散...'
});

// 可以通过props接收外部数据
const props = defineProps({
  status: {
    type: String,
    default: '密度过高'
  },
  statusType: {
    type: String,
    default: 'red'
  },
  description: {
    type: String,
    default: '当前状态：正在疏散...'
  }
});

// 使用props更新本地状态
if (props.status) buildingStatus.value.status = props.status;
if (props.statusType) buildingStatus.value.statusType = props.statusType;
if (props.description) buildingStatus.value.description = props.description;
</script>

<template>
  <div class="panel-section building-status">
    <div class="status-title">
      <span style="width: 3px; height: 16px; background-color: #2997e3; margin-right: 8px;"></span>
      建筑状态
    </div>
    <div class="status-content">
      <div class="status-circle">
        <div class="circle" :class="buildingStatus.statusType">
          {{ buildingStatus.status }}
        </div>
      </div>
      <div class="status-text">{{ buildingStatus.description }}</div>
    </div>
  </div>
</template>

<style scoped>
/* 面板通用样式 */
.panel-section {
  margin-bottom: 5px;
  border: 1px solid #1e3a5f;
  border-radius: 4px;
  background-color: #0d2b55;
}

.status-title {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  font-size: 0.9rem;
  font-weight: bold;
  color: white;
}

/* 建筑状态样式 */
.status-content {
  padding: 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.status-circle {
  margin-bottom: 15px;
}

.circle {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  color: white;
}

.circle.red {
  background-color: #ff4d4f;
}

.circle.yellow {
  background-color: #faad14;
}

.circle.green {
  background-color: #52c41a;
}

.status-text {
  font-size: 0.9rem;
  color: #8ebbff;
}
</style>
