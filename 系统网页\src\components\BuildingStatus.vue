<script setup>
// 接收来自App.vue的建筑状态数据和报警状态
const props = defineProps({
  // 建筑状态对象，包含status、statusType、description
  buildingStatus: {
    type: Object,
    default: () => ({
      status: '正常状态',
      statusType: 'green',
      description: '系统运行正常'
    })
  },
  // 报警状态对象，包含alert、lastChecked等
  alertStatus: {
    type: Object,
    default: () => ({
      alert: false,
      lastChecked: null
    })
  }
});
</script>

<template>
  <div class="panel-section building-status">
    <div class="status-title">
      <span style="width: 3px; height: 16px; background-color: #2997e3; margin-right: 8px;"></span>
      建筑状态
    </div>
    <div class="status-content">
      <div class="status-circle">
        <div class="circle" :class="props.buildingStatus.statusType">
          {{ props.buildingStatus.status }}
        </div>
      </div>
      <div class="status-text">{{ props.buildingStatus.description }}</div>

      <!-- 显示最后检查时间（仅在有数据时显示） -->
      <div v-if="props.alertStatus.lastChecked" class="last-check">
        最后检查: {{ props.alertStatus.lastChecked }}
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 面板通用样式 */
.panel-section {
  margin-bottom: 5px;
  border: 1px solid #1e3a5f;
  border-radius: 4px;
  background-color: #0d2b55;
}

.status-title {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  font-size: 0.9rem;
  font-weight: bold;
  color: white;
}

/* 建筑状态样式 */
.status-content {
  padding: 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.status-circle {
  margin-bottom: 15px;
}

.circle {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  color: white;
}

.circle.red {
  background-color: #ff4d4f;
}

.circle.yellow {
  background-color: #faad14;
}

.circle.green {
  background-color: #52c41a;
}

.status-text {
  font-size: 0.9rem;
  color: #8ebbff;
}

.last-check {
  font-size: 0.8rem;
  color: #999;
  margin-top: 10px;
}
</style>
