<script setup>
import { ref, nextTick } from 'vue';

// 模拟数据
const statusReports = ref([
  { time: '2022/08/23/03912', reporter: '张物管', content: '火灾' },
  { time: '2022/08/23/03912', reporter: '张物管', content: '火灾' },
  { time: '2022/08/23/03912', reporter: '张物管', content: '火灾' },
  { time: '2022/08/23/03912', reporter: '张物管', content: '火灾' },
  { time: '2022/08/23/03912', reporter: '张物管', content: '火灾' },
  { time: '2022/08/23/03912', reporter: '张物管', content: '火灾' },
  { time: '2022/08/23/03912', reporter: '张物管', content: '火灾' }
]);

// 编辑状态追踪
const editingCell = ref({ row: null, col: null });
const editValue = ref('');

const startEdit = (rowIdx, col) => {
  editingCell.value = { row: rowIdx, col };
  editValue.value = statusReports.value[rowIdx][col];
  nextTick(() => {
    const input = document.querySelector('.editing-input');
    if (input) input.focus();
  });
};

const saveEdit = (rowIdx, col) => {
  if (editValue.value !== undefined) {
    statusReports.value[rowIdx][col] = editValue.value;
  }
  editingCell.value = { row: null, col: null };
};

const onInputKey = (e, rowIdx, col) => {
  if (e.key === 'Enter') {
    saveEdit(rowIdx, col);
  } else if (e.key === 'Escape') {
    editingCell.value = { row: null, col: null };
  }
};

// 可以通过props接收外部数据
const props = defineProps({
  reports: {
    type: Array,
    default: () => []
  }
});

// 如果有外部数据，使用外部数据
if (props.reports && props.reports.length > 0) {
  statusReports.value = props.reports;
}

// 暴露方法给父组件
const emit = defineEmits(['update-report']);

const handleReportUpdate = (index, field, value) => {
  emit('update-report', { index, field, value });
};
</script>

<template>
  <div class="panel-section status-report">
    <div class="status-title">
      <span style="width: 3px; height: 16px; background-color: #2997e3; margin-right: 8px;"></span>
      实时状态上报
    </div>
    <div class="report-table">
      <div class="table-header">
        <div class="header-cell">告警时间</div>
        <div class="header-cell">上报人员</div>
        <div class="header-cell">上报内容</div>
      </div>
      <div class="table-body">
        <div v-for="(report, index) in statusReports" :key="index" class="table-row">
          <div class="cell" @dblclick="startEdit(index, 'time')">
            <template v-if="editingCell.row === index && editingCell.col === 'time'">
              <input 
                class="editing-input" 
                v-model="editValue" 
                @blur="saveEdit(index, 'time')" 
                @keyup="onInputKey($event, index, 'time')" 
              />
            </template>
            <template v-else>{{ report.time }}</template>
          </div>
          <div class="cell" @dblclick="startEdit(index, 'reporter')">
            <template v-if="editingCell.row === index && editingCell.col === 'reporter'">
              <input 
                class="editing-input" 
                v-model="editValue" 
                @blur="saveEdit(index, 'reporter')" 
                @keyup="onInputKey($event, index, 'reporter')" 
              />
            </template>
            <template v-else>{{ report.reporter }}</template>
          </div>
          <div class="cell" @dblclick="startEdit(index, 'content')">
            <template v-if="editingCell.row === index && editingCell.col === 'content'">
              <input 
                class="editing-input" 
                v-model="editValue" 
                @blur="saveEdit(index, 'content')" 
                @keyup="onInputKey($event, index, 'content')" 
              />
            </template>
            <template v-else>{{ report.content }}</template>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 面板通用样式 */
.panel-section {
  margin-bottom: 5px;
  border: 1px solid #1e3a5f;
  border-radius: 4px;
  background-color: #0d2b55;
}

.status-title {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  font-size: 0.9rem;
  font-weight: bold;
  color: white;
}

/* 实时状态上报表格样式 */
.report-table {
  width: 100%;
}

.table-header {
  display: flex;
  background-color: rgba(255, 255, 255, 0.1);
  font-weight: bold;
}

.header-cell {
  flex: 1;
  padding: 3px;
  text-align: center;
  font-size: 0.8rem;
  color: #1e90ff;
}

.header-cell:last-child {
  border-right: none;
}

.table-body {
  max-height: 400px;
  overflow-y: auto;
}

.table-row {
  display: flex;
}

.cell {
  flex: 1;
  padding: 6px;
  text-align: center;
  font-size: 0.8rem;
  color: white;
  cursor: pointer;
}

.cell:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.cell:last-child {
  border-right: none;
}

.editing-input {
  width: 100%;
  height: 28px;
  background: transparent;
  border: 1px solid #2997e3;
  color: #1e90ff;
  font-size: 0.8rem;
  text-align: center;
  outline: none;
  border-radius: 3px;
  box-sizing: border-box;
}
</style>
