<script setup>
// 接收来自App.vue的状态上报数据
const props = defineProps({
  reports: {
    type: Array,
    default: () => []
  },
  alertStatus: {
    type: Object,
    default: () => ({})
  }
});
</script>

<template>
  <div class="panel-section status-report">
    <div class="status-title">
      <span style="width: 3px; height: 16px; background-color: #2997e3; margin-right: 8px;"></span>
      实时状态上报
    </div>
    <div class="report-table">
      <div class="table-header">
        <div class="header-cell">告警时间</div>
        <div class="header-cell">上报人员</div>
        <div class="header-cell">上报内容</div>
      </div>
      <div class="table-body">
        <!-- 显示空状态提示 -->
        <div v-if="!props.reports || props.reports.length === 0" class="empty-state">
          <div class="empty-text">暂无上报记录</div>
        </div>

        <!-- 显示上报记录 -->
        <div v-for="(report, index) in props.reports" :key="index" class="table-row">
          <div class="cell">{{ report.time }}</div>
          <div class="cell">{{ report.reporter }}</div>
          <div class="cell">{{ report.content }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 面板通用样式 */
.panel-section {
  margin-bottom: 5px;
  border: 1px solid #1e3a5f;
  border-radius: 4px;
  background-color: #0d2b55;
}

.status-title {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  font-size: 0.9rem;
  font-weight: bold;
  color: white;
}

/* 实时状态上报表格样式 */
.report-table {
  width: 100%;
}

.table-header {
  display: flex;
  background-color: rgba(255, 255, 255, 0.1);
  font-weight: bold;
}

.header-cell {
  flex: 1;
  padding: 3px;
  text-align: center;
  font-size: 0.8rem;
  color: #1e90ff;
}

.header-cell:last-child {
  border-right: none;
}

.table-body {
  max-height: 400px;
  overflow-y: auto;
}

.table-row {
  display: flex;
}

.cell {
  flex: 1;
  padding: 6px;
  text-align: center;
  font-size: 0.8rem;
  color: white;
}

.cell:last-child {
  border-right: none;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
}

.empty-text {
  color: #8c8c8c;
  font-size: 0.9rem;
}
</style>
