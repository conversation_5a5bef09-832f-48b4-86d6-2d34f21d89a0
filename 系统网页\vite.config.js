import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import { networkInterfaces } from 'os'

// 获取本机IPv4地址
function getLocalIPv4() {
  const interfaces = networkInterfaces()
  for (const name of Object.keys(interfaces)) {
    for (const netInterface of interfaces[name]) {
      // 跳过内部地址和IPv6地址
      if (netInterface.family === 'IPv4' && !netInterface.internal) {
        return netInterface.address
      }
    }
  }
  return 'localhost'
}

const localIP = getLocalIPv4()
console.log(`🌐 本机IPv4地址: ${localIP}`)

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
  // 配置静态资源处理
  publicDir: 'public',
  // 配置开发服务器
  server: {
    host: '0.0.0.0', // 监听所有网络接口
    port: 3000,
    open: `http://${localIP}:3000`, // 使用本机IP打开浏览器
    // 配置代理以解决跨域问题
    proxy: {
      '/api': {
        target: 'http://************:5050',
        changeOrigin: true,
        secure: false,
      }
    }
  },
})
