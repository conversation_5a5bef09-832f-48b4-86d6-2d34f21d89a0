<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import HeaderNavigation from './components/HeaderNavigation.vue';
import LeftPanel from './components/LeftPanel.vue';
import CenterPanel from './components/CenterPanel.vue';
import RightPanel from './components/RightPanel.vue';

// 手报API配置
const ALARM_API_BASE = 'http://************:5005';

// 全局报警状态管理
const alertStatus = ref({
  alert: false,
  lastChecked: null,
  isPolling: false,
  pollInterval: null,
  currentGuide: null // 当前选中的引导员
});

// 系统状态数据
const systemStatus = ref({
  building: {
    status: '正常状态',
    statusType: 'green',
    description: '系统运行正常'
  },
  currentFloor: 'B1',
  mapScale: 1
});

// 地图标题状态
const mapTitle = ref('');

// 指示牌数据（保留用于其他组件可能的引用）
const signList = ref([]);

// 引导员数据
const guideList = ref([
  {
    id: 'G001',
    name: '李师傅',
    position: '地铁保安',
    location: 'B1层A区',
    phone: '18569874125',
    status: 'online'
  },
  {
    id: 'G002',
    name: '王师傅',
    position: '安全员',
    location: 'B2层B区',
    phone: '13812345678',
    status: 'busy'
  },
  {
    id: 'G003',
    name: '张师傅',
    position: '疏散员',
    location: 'M层C区',
    phone: '15987654321',
    status: 'offline'
  }
]);

// 状态上报数据（清空模拟数据，改为动态管理）
const statusReports = ref([]);

// 组件引用
const leftPanelRef = ref(null);
const centerPanelRef = ref(null);
const rightPanelRef = ref(null);

// ===== 手报API调用函数 =====
// 查询报警器状态
const checkAlarmStatus = async () => {
  try {
    const response = await fetch(`${ALARM_API_BASE}/status`);
    if (response.ok) {
      const data = await response.json();
      const newAlertStatus = data.alert || false;

      // 状态变化时更新系统状态
      if (newAlertStatus !== alertStatus.value.alert) {
        alertStatus.value.alert = newAlertStatus;
        updateBuildingStatus(newAlertStatus);

        // 状态变化时调整轮询频率
        if (newAlertStatus) {
          startHighFrequencyPolling(); // 报警状态：5秒轮询
        } else {
          startNormalPolling(); // 正常状态：30秒轮询
        }
      }

      alertStatus.value.lastChecked = new Date().toLocaleString();
      console.log('报警器状态查询成功:', data);
    } else {
      console.error('查询报警器状态失败:', response.status);
    }
  } catch (error) {
    console.error('报警器状态API连接失败:', error);
  }
};

// 触发报警器
const triggerAlarm = async (alarmData) => {
  try {
    const response = await fetch(ALARM_API_BASE, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(alarmData)
    });

    if (response.ok) {
      console.log('报警器触发成功');
      // 立即查询状态确认
      await checkAlarmStatus();
      return true;
    } else {
      console.error('触发报警器失败:', response.status);
      return false;
    }
  } catch (error) {
    console.error('报警器触发API连接失败:', error);
    return false;
  }
};

// 更新建筑状态显示
const updateBuildingStatus = (isAlert) => {
  if (isAlert) {
    systemStatus.value.building.status = '紧急状态';
    systemStatus.value.building.statusType = 'red';
    systemStatus.value.building.description = '手动报警器已触发';
  } else {
    systemStatus.value.building.status = '正常状态';
    systemStatus.value.building.statusType = 'green';
    systemStatus.value.building.description = '系统运行正常';
  }
};

// 开始正常频率轮询（30秒）
const startNormalPolling = () => {
  stopPolling();
  alertStatus.value.pollInterval = setInterval(() => {
    checkAlarmStatus();
  }, 30000);
  alertStatus.value.isPolling = true;
  console.log('开始正常频率轮询（30秒）');
};

// 开始高频轮询（5秒）
const startHighFrequencyPolling = () => {
  stopPolling();
  alertStatus.value.pollInterval = setInterval(() => {
    checkAlarmStatus();
  }, 5000);
  alertStatus.value.isPolling = true;
  console.log('开始高频轮询（5秒）');
};

// 停止轮询
const stopPolling = () => {
  if (alertStatus.value.pollInterval) {
    clearInterval(alertStatus.value.pollInterval);
    alertStatus.value.pollInterval = null;
  }
  alertStatus.value.isPolling = false;
};

// 添加状态上报记录
const addStatusReport = (reportData) => {
  statusReports.value.unshift(reportData);
  // 保持记录数量在20条以内
  if (statusReports.value.length > 20) {
    statusReports.value.pop();
  }
};

// ===== 左侧面板事件处理 =====
const handleReportUpdated = (updateData) => {
  console.log('状态上报更新:', updateData);
  // 这里可以调用API更新后端数据
};

const handleSimulationStarted = () => {
  console.log('疏散仿真开始');
  systemStatus.value.building.description = '正在进行疏散仿真...';
  // 触发中间面板的疏散模拟
  centerPanelRef.value?.startEvacuationSimulation();
};

const handleSimulationCompleted = () => {
  console.log('疏散仿真完成');
  systemStatus.value.building.description = '疏散仿真已完成';
};

const handleOptimizationStarted = () => {
  console.log('路径优化开始');
  systemStatus.value.building.description = '正在进行路径优化...';
};

const handleOptimizationCompleted = () => {
  console.log('路径优化完成');
  systemStatus.value.building.description = '路径优化已完成';
};

const handleSignUpdateStarted = () => {
  console.log('指示牌更新开始');
  systemStatus.value.building.description = '正在更新指示牌...';
};

const handleSignUpdateCompleted = () => {
  console.log('指示牌更新完成');
  systemStatus.value.building.description = '指示牌更新已完成';
};

// ===== 中间面板事件处理 =====
const handleFloorChanged = (floor) => {
  systemStatus.value.currentFloor = floor;
  console.log(`楼层切换到: ${floor}`);
};

const handleZoomChanged = (scale) => {
  systemStatus.value.mapScale = scale;
  console.log(`地图缩放: ${scale}`);
};

const handleMapControlChanged = (controlData) => {
  console.log('地图控制变更:', controlData);
};

const handleEvacuationSimulationComplete = (data) => {
  console.log('疏散模拟完成:', data);
  systemStatus.value.building.description = '疏散模拟已完成';
};

const handleEvacuationDataUpdated = (data) => {
  console.log('疏散数据更新:', data.length, '个数据点');
};

const handleFlowDataUpdated = (data) => {
  console.log('客流数据更新:', data.length, '个数据点');
};

const handleFlowChartClicked = (params) => {
  console.log('客流图表点击:', params);
};

// ===== 右侧面板事件处理 =====
// 视频监控事件
const handleVideoConnected = (cameraId) => {
  console.log(`视频连接成功: ${cameraId}`);
};

const handleVideoDisconnected = () => {
  console.log('视频连接断开');
};

const handleVideoError = (error) => {
  console.error('视频错误:', error);
};

const handleCameraSwitched = (cameraId) => {
  console.log(`摄像头切换到: ${cameraId}`);
};

const handleQualityChanged = (quality) => {
  console.log(`视频质量调整为: ${quality}`);
};

const handleScreenshotTaken = (data) => {
  console.log('截图已保存:', data);
};

// 指示牌管理事件
const handleDeviceStatusChecked = (statusData) => {
  console.log('设备状态查询:', statusData);
};

const handleMessageSent = (messageData) => {
  console.log('消息发送:', messageData);
};

// 引导员管理事件
const handleGuideSelected = (guide) => {
  console.log('选中引导员:', guide);
};

const handleTaskAssigned = (task) => {
  console.log('任务派发:', task);
  // 更新引导员状态
  const guideIndex = guideList.value.findIndex(guide => guide.id === task.guideId);
  if (guideIndex !== -1) {
    guideList.value[guideIndex].status = 'busy';
  }
};

const handleVoiceCallStarted = (callData) => {
  console.log('语音通话开始:', callData);
};

const handleGuideMessageSent = (messageData) => {
  console.log('引导员消息发送:', messageData);
};

const handleLocationUpdateRequested = (guideId) => {
  console.log(`请求位置更新: ${guideId}`);
};

const handleEmergencyCall = async (emergencyData) => {
  console.log('手动报警器触发:', emergencyData);

  // 更新当前选中引导员
  alertStatus.value.currentGuide = emergencyData.guide;

  // 构建报警数据
  const alarmData = {
    message: "手动报警器触发",
    recipients: ["2015559", "2515523"],
    alert: true
  };

  // 触发报警器
  const success = await triggerAlarm(alarmData);

  if (success) {
    // 添加状态上报记录
    const reportData = {
      time: new Date().toLocaleString().replace(/\//g, '/').replace(/:/g, ':'),
      reporter: emergencyData.guide ? emergencyData.guide.name : '未知',
      content: emergencyData.alarmType || '手动报警器触发'
    };
    addStatusReport(reportData);
  }
};

// 地图标题更新事件处理
const handleMapTitleUpdated = (title) => {
  mapTitle.value = title;
  console.log(`地图标题更新为: ${title}`);
};

// ===== 生命周期钩子 =====
onMounted(() => {
  // 组件挂载后立即查询一次状态并开始轮询
  checkAlarmStatus();
  startNormalPolling();
  console.log('手报功能初始化完成');
});

onUnmounted(() => {
  // 组件卸载时停止轮询
  stopPolling();
  console.log('手报功能清理完成');
});
</script>

<template>
  <div class="evacuation-system">
    <!-- 顶部导航栏 -->
    <HeaderNavigation :map-title="mapTitle" />

    <!-- 主体内容区 -->
    <div class="main-content">
      <!-- 左侧区域 -->
      <LeftPanel
        ref="leftPanelRef"
        :building-status="systemStatus.building"
        :reports="statusReports"
        :alert-status="alertStatus"
        @report-updated="handleReportUpdated"
        @simulation-started="handleSimulationStarted"
        @simulation-completed="handleSimulationCompleted"
        @optimization-started="handleOptimizationStarted"
        @optimization-completed="handleOptimizationCompleted"
        @sign-update-started="handleSignUpdateStarted"
        @sign-update-completed="handleSignUpdateCompleted"
      />

      <!-- 中间区域 -->
      <CenterPanel
        ref="centerPanelRef"
        :initial-floor="systemStatus.currentFloor"
        :initial-scale="systemStatus.mapScale"
        :auto-start-flow-chart="true"
        :auto-start-evacuation="false"
        @floor-changed="handleFloorChanged"
        @zoom-changed="handleZoomChanged"
        @map-control-changed="handleMapControlChanged"
        @evacuation-simulation-complete="handleEvacuationSimulationComplete"
        @evacuation-data-updated="handleEvacuationDataUpdated"
        @flow-data-updated="handleFlowDataUpdated"
        @flow-chart-clicked="handleFlowChartClicked"
        @map-title-updated="handleMapTitleUpdated"
      />

      <!-- 右侧区域 -->
      <RightPanel
        ref="rightPanelRef"
        :guides="guideList"
        :alert-status="alertStatus"
        :device-i-p="'10.10.52.37'"
        :device-port="80"
        :username="'admin'"
        :password="'hucom12345'"
        :auto-connect-video="false"
        :show-video-controls="true"
        :default-camera="1"
        @video-connected="handleVideoConnected"
        @video-disconnected="handleVideoDisconnected"
        @video-error="handleVideoError"
        @camera-switched="handleCameraSwitched"
        @quality-changed="handleQualityChanged"
        @screenshot-taken="handleScreenshotTaken"
        @device-status-checked="handleDeviceStatusChecked"
        @message-sent="handleMessageSent"
        @guide-selected="handleGuideSelected"
        @task-assigned="handleTaskAssigned"
        @voice-call-started="handleVoiceCallStarted"
        @guide-message-sent="handleGuideMessageSent"
        @location-update-requested="handleLocationUpdateRequested"
        @emergency-call="handleEmergencyCall"
      />
    </div>
  </div>
</template>

<style scoped>
/* 全局样式 */
.evacuation-system {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #0d325f;
  color: white;
  font-family: Arial, sans-serif;
  overflow: visible;
  min-width: 1200px;
}

/* 主体内容区样式 */
.main-content {
  display: flex;
  flex: 1;
  overflow: visible;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .evacuation-system {
    min-width: 1000px;
  }
}

@media (max-width: 768px) {
  .evacuation-system {
    min-width: 100%;
    min-height: 100vh;
  }

  .main-content {
    flex-direction: column;
    overflow-y: visible;
  }
}

@media (max-width: 480px) {
  .evacuation-system {
    font-size: 0.9rem;
  }
}
</style>
