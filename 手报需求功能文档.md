# 手报需求功能文档

## 项目概述

基于现有的智能疏散指示系统，集成手动报警器功能，实现统一的报警状态管理和实时监控。

## 功能需求

### 1. 建筑状态显示改进 (`BuildingStatus.vue`)

#### 1.1 状态标题保持
- 标题：`建筑状态`（保持不变）

#### 1.2 状态内容适配
**状态圆圈显示**：
- `正常状态` - 绿色圆圈，当 `alert: false` 时
- `紧急状态` - 红色圆圈，当 `alert: true` 时

**状态描述文字**：
- 正常时：`系统运行正常`
- 报警时：`手动报警器已触发`

### 2. 引导员管理改进 (`GuideManagement.vue`)

#### 2.1 紧急按钮功能变更
- 按钮标题：保持 `紧急`
- 功能变更：从引导员紧急呼叫 → 手动报警器触发按钮
- 点击后：发送POST请求到 `http://************:5005`（触发报警器）

#### 2.2 数据格式
```json
{
    "message": "手动报警器触发",
    "recipients": ["2015559", "2515523"],
    "alert": true
}
```

**注意**：后端只返回报警器状态 `{"alert": true/false}`，不处理其他业务逻辑

### 3. 实时状态上报改进 (`StatusReport.vue`)

#### 3.1 移除模拟数据
- 删除所有现有的模拟上报记录
- 初始状态为空表格

#### 3.2 报警上报功能（前端处理）
**触发时机**：点击"紧急"按钮后，前端自动添加记录

**上报内容选择**（前端弹窗）：
- `火灾`
- `人群骚乱`
- `地震`
- `袭击事件`
- `自定义内容`（提供输入框）

**记录格式**（前端生成）：
```javascript
{
    time: '2024/01/15/14:30:25',
    reporter: '李师傅',  // 当前选中的引导员姓名，引导员管理目前选择的人员（class="guide-title"-》“class="guide-selector"”）作为上报人员
    content: '选择的报警类型或自定义内容'
}
```

### 4. API集成方案

#### 4.1 接口地址
- **触发报警**：`POST http://************:5005`
- **查询状态**：`GET http://************:5005/status`

**重要**：`************:5005` 是独立的报警器端点，不影响现有系统其他API

#### 4.2 API响应格式
- **状态查询响应**：仅返回 `{"alert": true}` 或 `{"alert": false}`
- **触发报警响应**：简单确认信息

#### 4.3 状态轮询策略
- **正常状态**：每30秒轮询一次
- **报警状态**：每5秒轮询一次（监控状态恢复）
- **手动触发**：点击按钮后立即查询

#### 4.4 API调用方式
参考现有人数识别功能，直接使用完整URL进行fetch请求：

```javascript
// 触发报警
const response = await fetch('http://************:5005', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(data)
});

// 查询状态  
const statusResponse = await fetch('http://************:5005/status');
```

**无需修改 `vite.config.js`**，保持现有配置不变。

## 技术实现

### 1. 组件修改清单

#### 1.1 `BuildingStatus.vue`
- 添加报警器状态查询功能
- 实现状态轮询机制
- 根据 `alert` 字段动态更新显示

#### 1.2 `GuideManagement.vue`
- 修改"紧急"按钮点击事件
- 添加报警类型选择弹窗（前端处理）
- 实现POST请求发送到报警器端点
- 传递当前选中引导员信息给上报组件

#### 1.3 `StatusReport.vue`
- 清空模拟数据
- 添加动态记录添加功能（前端管理）
- 实现报警记录本地存储
- 接收当前选中引导员作为上报人员

#### 1.4 `App.vue`
- 添加全局报警状态管理
- 实现组件间数据传递
- 统一错误处理

### 2. 状态管理流程

```
用户点击"紧急"按钮 
    ↓
前端弹窗选择报警类型
    ↓
发送POST请求到报警器端点
    ↓
查询GET状态确认
    ↓
前端更新建筑状态显示
    ↓
前端添加上报记录（使用当前选中引导员）
    ↓
开始高频轮询(5秒)
    ↓
检测到状态恢复
    ↓
前端更新为正常状态
    ↓
恢复低频轮询(30秒)
```

### 3. 数据处理原则

- **后端职责**：仅处理报警器硬件状态
- **前端职责**：处理所有业务逻辑、用户交互、数据展示
- **状态同步**：定期轮询保持前后端状态一致

### 4. 代码开发原则

- **脚本模块化**：组件功能独立，职责清晰
- **简洁高效**：避免冗余代码，优化性能
- **无效代码清理**：移除未使用的函数和变量
- **可维护性**：代码结构清晰，注释完整

## 测试验证

### 1. 功能测试
- 使用 `发送测试灯牌和get.py` 验证API连通性
- 测试状态切换的实时性
- 验证轮询机制的准确性

### 2. 界面测试
- 确认状态显示的视觉效果
- 验证报警类型选择功能
- 测试上报记录的正确性

## 部署说明

### 1. 开发环境
```bash
npm run dev
# 访问: http://localhost:3000
```

### 2. 生产环境
- 确保报警器API服务 `http://************:5005` 可访问
- 配置正确的代理设置（不影响现有配置）
- 测试跨域请求正常

## 注意事项

1. **API独立性**：报警器API独立于现有系统，不影响其他功能
2. **网络要求**：需要能访问 `************:5005` 端口
3. **状态同步**：轮询频率需要平衡实时性和性能
4. **前端处理**：所有业务逻辑由前端处理，后端仅返回硬件状态
5. **数据持久化**：上报记录仅在前端保存，刷新后丢失

## 后续扩展

1. **历史记录**：添加报警历史查询功能
2. **通知机制**：集成浏览器通知API
3. **权限管理**：添加操作权限验证
4. **数据统计**：报警频率和类型统计


